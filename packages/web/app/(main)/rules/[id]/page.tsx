'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Select, Text, <PERSON>Area, TextField } from '@radix-ui/themes';
import { ArrowLeft, Copy, Download, Edit, ExternalLink, Save, Share, X } from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { useRule, useUpdateRule } from '@/hooks/use-rule-queries';
import { useSession } from '@/lib/auth-client';
import type { ApplyType, VisibilityType, Rule } from '@/lib/store';

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

// Type for form data
interface FormData {
  title: string;
  description: string;
  content: string;
  visibility: VisibilityType;
  applyType: ApplyType;
  glob: string;
  tags: string[];
}

// Type for form errors
interface FormErrors {
  title: string;
  content: string;
}

// Type for update payload that matches API expectations
interface RuleUpdatePayload {
  title: string;
  description: string | null;
  content: string;
  visibility: VisibilityType;
  applyType: ApplyType;
  glob: string | undefined;
  tags: string[];
}

// Helper function to validate form data
function validateFormData(formData: { title: string; content: string }): {
  title: string;
  content: string;
} {
  const errors = { title: '', content: '' };

  if (!formData.title.trim()) {
    errors.title = 'Title is required';
  }

  if (!formData.content.trim()) {
    errors.content = 'Content is required';
  }

  return errors;
}

// Helper function to create update data
function createUpdateData(formData: {
  title: string;
  description: string;
  content: string;
  visibility: VisibilityType;
  applyType: ApplyType;
  glob: string;
  tags: string[];
}): RuleUpdatePayload {
  return {
    title: formData.title.trim(),
    description: formData.description.trim() || null,
    content: formData.content.trim(),
    visibility: formData.visibility,
    applyType: formData.applyType,
    glob: formData.glob.trim() || undefined,
    tags: formData.tags.filter((tag) => tag.trim().length > 0),
  };
}

// Custom hooks for form management
function useRuleForm(rule: Rule | undefined, isEditing: boolean) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    visibility: 'PRIVATE' as VisibilityType,
    applyType: 'manual' as ApplyType,
    glob: '',
    tags: [] as string[],
  });

  const [formErrors, setFormErrors] = useState({
    title: '',
    content: '',
  });

  // Initialize form data when rule loads or editing starts
  useEffect(() => {
    if (rule && isEditing) {
      setFormData({
        title: rule.title,
        description: rule.description || '',
        content: rule.content,
        visibility: rule.visibility,
        applyType: rule.applyType,
        glob: rule.glob || '',
        tags: rule.tags.map(
          (t: { tag: { id: string; name: string; color: string } }) => t.tag.name
        ),
      });
    }
  }, [rule, isEditing]);

  const validateForm = () => {
    const errors = validateFormData(formData);
    setFormErrors(errors);
    return !errors.title && !errors.content;
  };

  const resetForm = () => {
    if (rule) {
      setFormData({
        title: rule.title,
        description: rule.description || '',
        content: rule.content,
        visibility: rule.visibility,
        applyType: rule.applyType,
        glob: rule.glob || '',
        tags: rule.tags.map(
          (t: { tag: { id: string; name: string; color: string } }) => t.tag.name
        ),
      });
    }
  };

  const clearErrors = () => {
    setFormErrors({ title: '', content: '' });
  };

  return {
    formData,
    setFormData,
    formErrors,
    setFormErrors,
    validateForm,
    resetForm,
    clearErrors,
  };
}

// Custom hook for rule actions
function useRuleActions(rule: Rule | undefined, ruleId: string) {
  const handleCopyContent = async () => {
    if (!rule) return;
    try {
      await navigator.clipboard.writeText(rule.content);
      toast.success('Rule content copied to clipboard');
    } catch (_err) {
      toast.error('Failed to copy content');
    }
  };

  const handleCopyLink = async () => {
    try {
      const url = `${window.location.origin}/r/${ruleId}`;
      await navigator.clipboard.writeText(url);
      toast.success('Rule link copied to clipboard');
    } catch (_err) {
      toast.error('Failed to copy link');
    }
  };

  const handleDownload = () => {
    if (!rule) return;
    const blob = new Blob([rule.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${rule.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Rule downloaded');
  };

  return {
    handleCopyContent,
    handleCopyLink,
    handleDownload,
  };
}

// Component for rule header
function RuleHeader({
  rule,
  isEditing,
  isOwner,
  formData,
  formErrors,
  setFormData,
  setFormErrors,
  onSave,
  onCancel,
  onEdit,
  onCopyContent,
  onCopyLink,
  onDownload,
  updateMutation,
}: {
  rule: Rule;
  isEditing: boolean;
  isOwner: boolean;
  formData: FormData;
  formErrors: FormErrors;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  setFormErrors: React.Dispatch<React.SetStateAction<FormErrors>>;
  onSave: () => void;
  onCancel: () => void;
  onEdit: () => void;
  onCopyContent: () => void;
  onCopyLink: () => void;
  onDownload: () => void;
  updateMutation: { isPending: boolean };
}) {
  return (
    <div className="flex items-start justify-between">
      <div className="mr-4 flex-1 space-y-2">
        <div className="flex items-center gap-2 text-muted-foreground text-sm">
          <Button variant="ghost" size="1" asChild>
            <Link href="/rules">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <span>Rules</span>
        </div>

        {isEditing ? (
          <div className="space-y-3">
            <div>
              <TextField.Root
                value={formData.title}
                onChange={(e) => {
                  setFormData((prev: FormData) => ({ ...prev, title: e.target.value }));
                  if (formErrors.title) {
                    setFormErrors((prev: FormErrors) => ({ ...prev, title: '' }));
                  }
                }}
                placeholder="Rule title"
                className="font-bold text-3xl"
                size="3"
                color={formErrors.title ? 'red' : undefined}
              />
              {formErrors.title && (
                <Text size="1" color="red" className="mt-1 block">
                  {formErrors.title}
                </Text>
              )}
            </div>
            <TextArea
              value={formData.description}
              onChange={(e) => setFormData((prev: FormData) => ({ ...prev, description: e.target.value }))}
              placeholder="Rule description (optional)"
              className="text-lg"
              rows={2}
            />
          </div>
        ) : (
          <>
            <h1 className="font-bold text-3xl">{rule.title}</h1>
            {rule.description && (
              <p className="text-lg text-muted-foreground">{rule.description}</p>
            )}
          </>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        {isOwner && isEditing ? (
          <>
            <Button
              variant="solid"
              size="2"
              onClick={onSave}
              disabled={updateMutation.isPending}
            >
              <Save className="mr-2 h-4 w-4" />
              {updateMutation.isPending ? 'Saving...' : 'Save'}
            </Button>
            <Button variant="outline" size="2" onClick={onCancel}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
          </>
        ) : (
          <>
            {isOwner && (
              <Button variant="solid" size="2" onClick={onEdit}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </Button>
            )}
            <Button variant="outline" size="2" onClick={onCopyContent}>
              <Copy className="mr-2 h-4 w-4" />
              Copy
            </Button>
            <Button variant="outline" size="2" onClick={onCopyLink}>
              <Share className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button variant="outline" size="2" onClick={onDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

// Component for rule metadata
function RuleMetadata({
  rule,
  isEditing,
  formData,
  setFormData,
}: {
  rule: Rule;
  isEditing: boolean;
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
}) {
  return (
    <Card className="p-4">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="flex items-center gap-2">
          <span className="font-medium text-sm">Visibility:</span>
          {isEditing ? (
            <Select.Root
              value={formData.visibility}
              onValueChange={(value) =>
                setFormData((prev: FormData) => ({ ...prev, visibility: value as VisibilityType }))
              }
            >
              <Select.Trigger className="w-24" />
              <Select.Content>
                <Select.Item value="PRIVATE">Private</Select.Item>
                <Select.Item value="PUBLIC">Public</Select.Item>
              </Select.Content>
            </Select.Root>
          ) : (
            <Badge variant={rule.visibility === 'PUBLIC' ? 'solid' : 'outline'}>
              {rule.visibility}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2">
          <span className="font-medium text-sm">Apply Type:</span>
          {isEditing ? (
            <Select.Root
              value={formData.applyType}
              onValueChange={(value) =>
                setFormData((prev: FormData) => ({ ...prev, applyType: value as ApplyType }))
              }
            >
              <Select.Trigger className="w-24" />
              <Select.Content>
                <Select.Item value="manual">Manual</Select.Item>
                <Select.Item value="auto">Auto</Select.Item>
                <Select.Item value="always">Always</Select.Item>
              </Select.Content>
            </Select.Root>
          ) : (
            <Badge variant="outline">{rule.applyType}</Badge>
          )}
        </div>

        {rule.user && (
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">Author:</span>
            <span className="text-sm">{rule.user.name || rule.user.email}</span>
          </div>
        )}
      </div>

      {/* Tags */}
      <div className="mt-4 border-t pt-4">
        <div className="flex flex-wrap items-center gap-2">
          <span className="font-medium text-sm">Tags:</span>
          {isEditing ? (
            <TextField.Root
              value={formData.tags.join(', ')}
              onChange={(e) =>
                setFormData((prev: FormData) => ({
                  ...prev,
                  tags: e.target.value
                    .split(',')
                    .map((tag) => tag.trim())
                    .filter((tag) => tag.length > 0),
                }))
              }
              placeholder="Enter tags separated by commas"
              className="flex-1"
            />
          ) : rule.tags && rule.tags.length > 0 ? (
            rule.tags.map((tagRelation) => (
              <Badge
                key={tagRelation.tag.id}
                variant="soft"
                style={{
                  backgroundColor: `${tagRelation.tag.color}20`,
                  color: tagRelation.tag.color,
                }}
              >
                {tagRelation.tag.name}
              </Badge>
            ))
          ) : (
            <span className="text-muted-foreground text-sm">No tags</span>
          )}
        </div>
      </div>
    </Card>
  );
}

// Component for rule content
function RuleContent({
  rule,
  isEditing,
  formData,
  formErrors,
  setFormData,
  setFormErrors,
  onCopyContent,
}: {
  rule: Rule;
  isEditing: boolean;
  formData: FormData;
  formErrors: FormErrors;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  setFormErrors: React.Dispatch<React.SetStateAction<FormErrors>>;
  onCopyContent: () => void;
}) {
  return (
    <Card className="p-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="font-semibold text-xl">Rule Content</h2>
          {!isEditing && (
            <Button variant="ghost" size="1" onClick={onCopyContent}>
              <Copy className="h-4 w-4" />
            </Button>
          )}
        </div>

        {isEditing ? (
          <div>
            <TextArea
              value={formData.content}
              onChange={(e) => {
                setFormData((prev: FormData) => ({ ...prev, content: e.target.value }));
                if (formErrors.content) {
                  setFormErrors((prev: FormErrors) => ({ ...prev, content: '' }));
                }
              }}
              placeholder="Enter your rule content here..."
              className="min-h-[300px] font-mono text-sm"
              rows={15}
              color={formErrors.content ? 'red' : undefined}
            />
            {formErrors.content && (
              <Text size="1" color="red" className="mt-1 block">
                {formErrors.content}
              </Text>
            )}
          </div>
        ) : (
          <div className="rounded-lg border bg-gray-50 p-4 dark:bg-gray-900">
            <pre className="overflow-x-auto whitespace-pre-wrap font-mono text-sm">
              {rule.content}
            </pre>
          </div>
        )}
      </div>
    </Card>
  );
}

export default function RulePage() {
  const params = useParams();
  const ruleId = params.id as string;
  const { data: session } = useSession();
  const [isEditing, setIsEditing] = useState(false);

  // Use the query hook instead of manual fetch
  const { data: rule, isLoading, error, isError } = useRule(ruleId);

  // Update rule mutation
  const updateRuleMutation = useUpdateRule();

  // Check if user is the owner of the rule
  const isOwner = session?.user?.id === rule?.userId;

  // Custom hooks
  const { formData, setFormData, formErrors, setFormErrors, validateForm, resetForm, clearErrors } =
    useRuleForm(rule, isEditing);

  const { handleCopyContent, handleCopyLink, handleDownload } = useRuleActions(rule, ruleId);

  // Handle save
  const handleSave = async () => {
    if (!rule) return;

    // Validate form
    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    try {
      const updateData = createUpdateData(formData);

      await updateRuleMutation.mutateAsync({
        id: rule.id,
        data: updateData as unknown as Partial<Rule>,
      });
      setIsEditing(false);
      clearErrors();
      toast.success('Rule updated successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update rule';
      toast.error(errorMessage);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setIsEditing(false);
    resetForm();
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="text-center">
          <h1 className="mb-4 font-bold text-2xl">Loading Rule...</h1>
          <p className="text-muted-foreground">Rule ID: {ruleId}</p>
          <div className="mt-4">
            <div className="animate-pulse">
              <div className="mx-auto h-4 w-3/4 rounded bg-gray-200 dark:bg-gray-700"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (isError || !rule) {
    const errorMessage = error instanceof Error ? error.message : 'Rule not found';

    return (
      <div className="container max-w-4xl py-8">
        <div className="text-center">
          <h1 className="mb-4 font-bold text-2xl text-red-600">Error</h1>
          <p className="mb-4 text-muted-foreground">{errorMessage}</p>
          <Button variant="outline" asChild>
            <Link href="/rules">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Rules
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl space-y-6 py-8">
      <RuleHeader
        rule={rule}
        isEditing={isEditing}
        isOwner={isOwner}
        formData={formData}
        formErrors={formErrors}
        setFormData={setFormData}
        setFormErrors={setFormErrors}
        onSave={handleSave}
        onCancel={handleCancel}
        onEdit={() => setIsEditing(true)}
        onCopyContent={handleCopyContent}
        onCopyLink={handleCopyLink}
        onDownload={handleDownload}
        updateMutation={updateRuleMutation}
      />

      <RuleMetadata
        rule={rule}
        isEditing={isEditing}
        formData={formData}
        setFormData={setFormData}
      />

      <RuleContent
        rule={rule}
        isEditing={isEditing}
        formData={formData}
        formErrors={formErrors}
        setFormData={setFormData}
        setFormErrors={setFormErrors}
        onCopyContent={handleCopyContent}
      />

      {/* Raw Data Link */}
      {rule.visibility === 'PUBLIC' && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Raw Data Access</h3>
              <p className="text-muted-foreground text-sm">
                Access the raw rule content directly via API
              </p>
            </div>
            <Button variant="outline" size="2" asChild>
              <Link href={`/api/rules/raw?id=${rule.id}`} target="_blank">
                <ExternalLink className="mr-2 h-4 w-4" />
                Raw Data
              </Link>
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
