'use client';

import { <PERSON><PERSON>, Button, Flex, Select, Text, TextArea, TextField } from '@radix-ui/themes';
import { Code, Plus, Settings, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { CodeEditor } from '@/components/ui/code-editor';
import { GlobPatternHelper } from '@/components/ui/glob-pattern-helper';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { formatRuleContent, parseRuleContent } from '@/lib/rule-sections';
import {
  APPLY_TYPE_METADATA,
  type ApplyType,
  type Rule,
  type RulePayload,
  type RuleSection,
  type VisibilityType,
} from '@/lib/store';
import { RuleSectionsList } from './rule-sections-list';

interface RuleEditorProps {
  rule?: Rule;
  onSave: (rule: Partial<RulePayload>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function RuleEditor({ rule, onSave, onCancel, isLoading = false }: RuleEditorProps) {
  const [title, setTitle] = useState(rule?.title || '');
  const [description, setDescription] = useState(rule?.description || '');
  const [sections, setSections] = useState<RuleSection[]>([]);
  const [rawContent, setRawContent] = useState(rule?.content || '');
  const [editingMode, setEditingMode] = useState<'simple' | 'advanced'>('simple');
  // IDE type is no longer used
  const [visibility, setVisibility] = useState(rule?.visibility || 'PRIVATE');
  const [applyType, setApplyType] = useState<ApplyType>(rule?.applyType || 'manual');
  const [glob, setGlob] = useState(rule?.glob || '');
  const [tags, setTags] = useState<string[]>(rule?.tags.map((t) => t.tag.name) || []);
  const [newTag, setNewTag] = useState('');

  // Initialize sections and raw content from rule content
  useEffect(() => {
    if (rule?.content) {
      const parsedSections = parseRuleContent(rule.content);
      setSections(parsedSections);
      setRawContent(rule.content);
    } else {
      // Create a default empty section for new rules
      const defaultSections = [
        {
          id: `section_${Date.now()}`,
          title: 'Section 1',
          content: '',
        },
      ];
      setSections(defaultSections);
      setRawContent('');
    }
  }, [rule?.content]);

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleModeChange = (newMode: 'simple' | 'advanced') => {
    if (newMode === editingMode) return;

    if (newMode === 'simple') {
      // Switching to simple mode: convert sections to raw content
      const formattedContent = formatRuleContent(sections);
      setRawContent(formattedContent);
    } else {
      // Switching to advanced mode: parse raw content into sections
      const parsedSections = parseRuleContent(rawContent);
      setSections(parsedSections);
    }

    setEditingMode(newMode);
  };

  const handleSectionsChange = (newSections: RuleSection[]) => {
    setSections(newSections);
    // Keep raw content in sync when in advanced mode
    if (editingMode === 'advanced') {
      const formattedContent = formatRuleContent(newSections);
      setRawContent(formattedContent);
    }
  };

  const handleRawContentChange = (newContent: string) => {
    setRawContent(newContent);
    // Keep sections in sync when in simple mode
    if (editingMode === 'simple') {
      const parsedSections = parseRuleContent(newContent);
      setSections(parsedSections);
    }
  };

  const handleSave = () => {
    // Use the current content based on the active mode
    const finalContent = editingMode === 'simple' ? rawContent : formatRuleContent(sections);

    onSave({
      title,
      description,
      content: finalContent,
      visibility: visibility as 'PRIVATE' | 'PUBLIC',
      applyType,
      glob: glob.trim() || undefined,
      tags,
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div>
          <Text as="label" htmlFor="title" size="2" weight="medium">
            Rule Title
          </Text>
          <TextField.Root
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter rule title..."
          />
        </div>

        <div>
          <Text as="label" htmlFor="description" size="2" weight="medium">
            Description
          </Text>
          <TextArea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe what this rule does..."
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 gap-4">
          <div>
            <Text as="label" htmlFor="visibility" size="2" weight="medium">
              Visibility
            </Text>
            <Select.Root
              value={visibility}
              onValueChange={(value) => setVisibility(value as VisibilityType)}
            >
              <Select.Trigger placeholder="Select visibility" />
              <Select.Content>
                <Select.Item value="PRIVATE">Private</Select.Item>
                <Select.Item value="PUBLIC">Public</Select.Item>
              </Select.Content>
            </Select.Root>
          </div>
        </div>

        <div>
          <Text as="label" htmlFor="apply-type" size="2" weight="medium">
            Apply Type
          </Text>
          <Select.Root
            value={applyType}
            onValueChange={(value) => setApplyType(value as ApplyType)}
          >
            <Select.Trigger placeholder="Select apply type" />
            <Select.Content>
              {Object.entries(APPLY_TYPE_METADATA).map(([key, metadata]) => (
                <Select.Item key={key} value={key}>
                  <div className="flex flex-col">
                    <span className="font-medium">{metadata.name}</span>
                    <span className="text-gray-500 text-xs">{metadata.description}</span>
                  </div>
                </Select.Item>
              ))}
            </Select.Content>
          </Select.Root>
        </div>

        <div>
          <Flex align="center" gap="2" mb="1">
            <Text as="label" htmlFor="glob-pattern" size="2" weight="medium">
              File Pattern (Optional)
            </Text>
            <GlobPatternHelper onPatternSelect={setGlob} />
          </Flex>
          <TextField.Root
            id="glob-pattern"
            value={glob}
            onChange={(e) => setGlob(e.target.value)}
            placeholder="e.g., src/**/*.{js,ts} or docs/**/*.md"
          />
          <Text size="1" color="gray" className="mt-1 block">
            Specify file patterns for auto-apply scenarios. Use glob patterns to target specific
            files.
          </Text>
        </div>

        <div>
          <Text as="label" size="2" weight="medium">
            Tags
          </Text>
          <Flex wrap="wrap" gap="2" mb="2">
            {tags.map((tag) => (
              <Badge key={tag} variant="soft" className="gap-1">
                {tag}
                <Button
                  variant="ghost"
                  size="1"
                  className="h-4 h-auto w-4 p-0"
                  onClick={() => handleRemoveTag(tag)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </Flex>
          <Flex gap="2">
            <TextField.Root
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add a tag..."
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
            />
            <Button onClick={handleAddTag} size="1">
              <Plus className="h-4 w-4" />
            </Button>
          </Flex>
        </div>

        {/* Rule Content Section with Mode Toggle */}
        <div className="space-y-4">
          <Flex justify="between" align="center">
            <Text as="label" size="2" weight="medium">
              Rule Content
            </Text>
            <ToggleGroup
              type="single"
              value={editingMode}
              onValueChange={(value) => value && handleModeChange(value as 'simple' | 'advanced')}
              className="rounded-md bg-gray-50 p-1 dark:bg-gray-800"
            >
              <ToggleGroupItem
                value="simple"
                className="flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700"
              >
                <Code className="h-4 w-4" />
                Simple
              </ToggleGroupItem>
              <ToggleGroupItem
                value="advanced"
                className="flex items-center gap-2 px-3 py-1.5 text-sm data-[state=on]:bg-white data-[state=on]:shadow-sm dark:data-[state=on]:bg-gray-700"
              >
                <Settings className="h-4 w-4" />
                Advanced
              </ToggleGroupItem>
            </ToggleGroup>
          </Flex>

          {editingMode === 'simple' ? (
            <div>
              <Text size="1" color="gray" className="mb-2 block">
                Edit your rule content in a simple code editor format
              </Text>
              <CodeEditor
                value={rawContent}
                onChange={handleRawContentChange}
                placeholder="Enter your rule content here..."
                className="min-h-[300px]"
              />
            </div>
          ) : (
            <div>
              <Text size="1" color="gray" className="mb-2 block">
                Edit your rule content using structured sections with metadata
              </Text>
              <RuleSectionsList sections={sections} onSectionsChange={handleSectionsChange} />
            </div>
          )}
        </div>
      </div>

      <Flex justify="end" gap="2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSave}>{rule ? 'Update Rule' : 'Create Rule'}</Button>
      </Flex>
    </div>
  );
}
